/**
 * Dhan WebSocket Configuration API Route
 * Provides WebSocket configuration for client-side connections
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(_request: NextRequest) {
  try {
    // Get credentials from environment
    const accessToken = process.env.DHAN_ACCESS_TOKEN;
    const clientId = process.env.DHAN_CLIENT_ID;

    if (!accessToken || !clientId) {
      return NextResponse.json(
        { 
          error: 'WebSocket credentials not configured',
          available: false
        },
        { status: 500 }
      );
    }

    // Return configuration for client-side WebSocket connection
    return NextResponse.json({
      available: true,
      config: {
        accessToken,
        clientId,
        version: 2,
        authType: 2,
        reconnectInterval: 30000, // 30 seconds to respect rate limits
        maxReconnectAttempts: 5, // Reduced attempts
        heartbeatInterval: 60000 // 60 seconds
      }
    });

  } catch (error) {
    console.error('Error in WebSocket config API route:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        available: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle CORS if needed
export async function OPTIONS(_request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
