/**
 * Trading Initialization API Route
 * Handles initializing trading setup for new users
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;

    console.log('Trading initialization request for userId:', userId);

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Call the database function to initialize trading setup
    const { data: initResult, error: initError } = await supabase
      .rpc('initialize_trading_user', { target_user_id: userId });

    if (initError) {
      console.error('Error initializing trading setup:', initError);
      return NextResponse.json(
        { 
          error: 'Failed to initialize trading setup',
          message: initError.message 
        },
        { status: 500 }
      );
    }

    console.log('Successfully initialized trading setup:', initResult);

    return NextResponse.json({
      success: true,
      data: initResult
    });

  } catch (error) {
    console.error('Error in trading initialization API:', error);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle CORS if needed
export async function OPTIONS(_request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
