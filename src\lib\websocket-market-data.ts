/**
 * Dhan WebSocket Market Data Service
 * Implements Dhan API v2 WebSocket specification with binary message handling
 */

import { ProcessedMarketData } from '@/types/dhan';
import { updateInstrumentInCache } from './historical-data-storage';
import {
  parseBinaryMessage,
  convertToProcessedMarketData,
  createAuthMessage,
  createSubscriptionMessage,
  FEED_REQUEST_CODES
} from './dhan-binary-parser';

export interface DhanWebSocketConfig {
  accessToken: string;
  clientId: string;
  reconnectInterval: number; // milliseconds
  maxReconnectAttempts: number;
  heartbeatInterval: number; // milliseconds
  version: number; // API version (2 for v2)
  authType: number; // Authentication type (2 by default)
}

// Dhan WebSocket message types
export interface DhanSubscriptionRequest {
  RequestCode: number;
  InstrumentCount: number;
  InstrumentList: Array<{
    ExchangeSegment: string;
    SecurityId: string;
  }>;
}

export interface DhanDisconnectRequest {
  RequestCode: number;
}

// Binary message structures
export interface DhanResponseHeader {
  feedResponseCode: number;
  messageLength: number;
  exchangeSegment: number;
  securityId: number;
}

export interface DhanTickerData {
  header: DhanResponseHeader;
  lastTradedPrice: number;
  lastTradeTime: number;
}

export interface DhanQuoteData {
  header: DhanResponseHeader;
  lastTradedPrice: number;
  lastTradedQuantity: number;
  lastTradeTime: number;
  averageTradePrice: number;
  volume: number;
  totalSellQuantity: number;
  totalBuyQuantity: number;
  dayOpen: number;
  dayClose: number;
  dayHigh: number;
  dayLow: number;
}

export interface DhanPrevCloseData {
  header: DhanResponseHeader;
  previousClose: number;
  openInterest: number;
}

export type WebSocketEventHandler = (data: ProcessedMarketData) => void;

// WebSocket message interface for internal communication
export interface WebSocketMessage {
  type: 'SUBSCRIBE' | 'UNSUBSCRIBE' | 'HEARTBEAT' | 'AUTH';
  data?: unknown;
  timestamp: string;
}

export class DhanWebSocketMarketDataService {
  private ws: WebSocket | null = null;
  private config: DhanWebSocketConfig;
  private isConnected = false;
  private isConnecting = false;
  private isAuthenticated = false;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private subscribedInstruments: Set<string> = new Set();
  private instrumentMap: Map<string, { symbol: string; name: string; previousClose?: number }> = new Map();
  private lastConnectionAttempt: number = 0;
  private connectionThrottleMs: number = 10000; // Minimum 10 seconds between connection attempts

  // Event handlers
  private onDataHandler: WebSocketEventHandler | null = null;
  private onConnectedHandler: (() => void) | null = null;
  private onDisconnectedHandler: (() => void) | null = null;
  private onErrorHandler: ((error: Error) => void) | null = null;

  constructor(config: DhanWebSocketConfig) {
    this.config = config;
    this.initializeInstrumentMap();
  }

  /**
   * Initialize instrument mapping for known instruments
   */
  private initializeInstrumentMap(): void {
    // Add known instruments - this should be configurable
    this.instrumentMap.set('NSE_EQ_26000', { symbol: 'NIFTY50', name: 'NIFTY 50' });
    this.instrumentMap.set('MCX_COMM_242', { symbol: 'SILVER', name: 'Silver' });
  }

  /**
   * Connect to Dhan WebSocket
   */
  async connect(): Promise<boolean> {
    if (this.isConnected || this.isConnecting) {
      return this.isConnected;
    }

    // Throttle connection attempts to respect rate limits
    const now = Date.now();
    const timeSinceLastAttempt = now - this.lastConnectionAttempt;
    if (timeSinceLastAttempt < this.connectionThrottleMs) {
      const waitTime = this.connectionThrottleMs - timeSinceLastAttempt;
      // Connection throttled, waiting before retry
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastConnectionAttempt = Date.now();
    this.isConnecting = true;
    
    try {
      // Construct Dhan WebSocket URL
      const wsUrl = `wss://api-feed.dhan.co?version=${this.config.version}&token=${this.config.accessToken}&clientId=${this.config.clientId}&authType=${this.config.authType}`;

      // Connecting to Dhan WebSocket

      this.ws = new WebSocket(wsUrl);
      this.ws.binaryType = 'arraybuffer'; // Important for binary message handling

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

      // Wait for connection and authentication
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          this.isConnecting = false;
          resolve(false);
        }, 15000); // 15 second timeout for connection + auth

        const checkAuth = () => {
          if (this.isAuthenticated) {
            clearTimeout(timeout);
            resolve(true);
          }
        };

        const originalOnOpen = this.ws!.onopen;
        this.ws!.onopen = (event) => {
          originalOnOpen?.call(this.ws!, event);
          // Check authentication status periodically
          const authCheck = setInterval(() => {
            if (this.isAuthenticated) {
              clearInterval(authCheck);
              checkAuth();
            }
          }, 100);

          // Stop checking after timeout
          setTimeout(() => clearInterval(authCheck), 15000);
        };
      });
    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
      return false;
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    // Disconnecting WebSocket
    
    this.clearTimers();
    this.subscribedInstruments.clear();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Subscribe to instrument updates using Dhan binary protocol
   */
  subscribe(instruments: string[]): boolean {
    if (!this.isConnected || !this.isAuthenticated) {
      console.warn('WebSocket not connected or authenticated, cannot subscribe');
      return false;
    }

    // Convert instrument symbols to Dhan format
    const dhanInstruments = instruments.map(symbol => {
      switch (symbol) {
        case 'NIFTY50':
          return { exchangeSegment: 'NSE_EQ', securityId: '26000' };
        case 'SILVER':
          return { exchangeSegment: 'MCX_COMM', securityId: '242' };
        default:
          console.warn(`Unknown instrument: ${symbol}`);
          return null;
      }
    }).filter(Boolean) as Array<{ exchangeSegment: string; securityId: string }>;

    if (dhanInstruments.length === 0) {
      console.error('No valid instruments to subscribe');
      return false;
    }

    try {
      // Subscribe to quote data (request code 16)
      const subscriptionMessage = createSubscriptionMessage(FEED_REQUEST_CODES.SUBSCRIBE_QUOTE, dhanInstruments);
      this.ws!.send(subscriptionMessage);

      instruments.forEach(instrument => this.subscribedInstruments.add(instrument));
      // Successfully subscribed to instruments
      return true;
    } catch (error) {
      console.error('Error subscribing to instruments:', error);
      return false;
    }
  }

  /**
   * Unsubscribe from instrument updates
   */
  unsubscribe(instruments: string[]): boolean {
    if (!this.isConnected) {
      return false;
    }

    const message: WebSocketMessage = {
      type: 'UNSUBSCRIBE',
      data: { instruments },
      timestamp: new Date().toISOString()
    };

    try {
      this.ws!.send(JSON.stringify(message));
      instruments.forEach(instrument => this.subscribedInstruments.delete(instrument));
      // Successfully unsubscribed from instruments
      return true;
    } catch (error) {
      console.error('Error unsubscribing from instruments:', error);
      return false;
    }
  }

  /**
   * Set event handlers
   */
  onData(handler: WebSocketEventHandler): void {
    this.onDataHandler = handler;
  }

  onConnected(handler: () => void): void {
    this.onConnectedHandler = handler;
  }

  onDisconnected(handler: () => void): void {
    this.onDisconnectedHandler = handler;
  }

  onError(handler: (error: Error) => void): void {
    this.onErrorHandler = handler;
  }

  /**
   * Get connection status
   */
  getStatus(): {
    isConnected: boolean;
    isConnecting: boolean;
    isAuthenticated: boolean;
    reconnectAttempts: number;
    subscribedCount: number;
    subscribedInstruments: string[];
  } {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      isAuthenticated: this.isAuthenticated,
      reconnectAttempts: this.reconnectAttempts,
      subscribedCount: this.subscribedInstruments.size,
      subscribedInstruments: Array.from(this.subscribedInstruments)
    };
  }

  // Private methods

  private handleOpen(): void {
    // WebSocket connection established
    this.isConnected = true;
    this.isConnecting = false;
    this.reconnectAttempts = 0;

    // Send binary authentication message
    this.authenticate();

    // Start heartbeat
    this.startHeartbeat();
  }

  private handleMessage(event: MessageEvent): void {
    try {
      // Handle binary messages from Dhan WebSocket
      if (event.data instanceof ArrayBuffer) {
        this.handleBinaryMessage(event.data);
      } else {
        // Handle any JSON messages (unlikely with Dhan API)
        // Received unexpected non-binary message
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      this.onErrorHandler?.(error instanceof Error ? error : new Error('Unknown WebSocket error'));
    }
  }

  private handleBinaryMessage(buffer: ArrayBuffer): void {
    try {
      const parsedData = parseBinaryMessage(buffer);

      if (parsedData.header.feedResponseCode === 50) {
        // Disconnection message
        // Received disconnection message from server
        this.handleDisconnection(parsedData.disconnectionCode || 0);
        return;
      }

      // Check if this is authentication confirmation
      if (!this.isAuthenticated && this.isConnected) {
        // First successful data message indicates authentication success
        this.isAuthenticated = true;
        // WebSocket authentication successful
        this.onConnectedHandler?.();
      }

      // Convert to ProcessedMarketData format
      if (parsedData.data && (parsedData.header.feedResponseCode === 2 || parsedData.header.feedResponseCode === 4)) {
        const marketData = convertToProcessedMarketData(parsedData.data as DhanTickerData | DhanQuoteData, this.instrumentMap);

        if (marketData) {
          this.handleMarketData(marketData);
        }
      }
    } catch (error) {
      console.error('Error parsing binary message:', error);
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('WebSocket disconnected:', event.code, event.reason);
    this.isConnected = false;
    this.isConnecting = false;
    
    this.clearTimers();
    this.onDisconnectedHandler?.();
    
    // Attempt reconnection if not a clean close
    if (event.code !== 1000 && this.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.onErrorHandler?.(new Error('WebSocket connection error'));
  }

  private authenticate(): void {
    try {
      const authMessage = createAuthMessage(this.config.clientId, this.config.accessToken);
      this.ws!.send(authMessage);
      // Authentication message sent
    } catch (error) {
      console.error('Error sending authentication:', error);
    }
  }

  private handleDisconnection(code: number): void {
    const disconnectionMessages: Record<number, string> = {
      805: 'Connection limit exceeded',
      806: 'Data APIs not subscribed',
      807: 'Access token is expired',
      808: 'Authentication Failed - Check Client ID',
      809: 'Access token is invalid',
      9475: 'Rate limit exceeded - Too many requests. Please wait before reconnecting.'
    };

    const message = disconnectionMessages[code] || `Unknown disconnection code: ${code}`;
    console.error('WebSocket disconnected by server:', message);

    // For rate limiting, don't attempt immediate reconnection
    if (code === 9475) {
      // Rate limiting detected, implementing extended backoff
      // Reset reconnect attempts to implement longer delays
      this.reconnectAttempts = Math.max(this.reconnectAttempts, 3);
    }

    this.onErrorHandler?.(new Error(message));
  }

  private handleMarketData(processedData: ProcessedMarketData): void {
    try {
      // Update cache with real-time data
      updateInstrumentInCache(processedData, 'WEBSOCKET');

      // Notify handlers
      this.onDataHandler?.(processedData);
    } catch (error) {
      console.error('Error handling market data:', error);
    }
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected && this.ws) {
        const heartbeat: WebSocketMessage = {
          type: 'HEARTBEAT',
          timestamp: new Date().toISOString()
        };
        
        try {
          this.ws.send(JSON.stringify(heartbeat));
        } catch (error) {
          console.error('Error sending heartbeat:', error);
        }
      }
    }, this.config.heartbeatInterval);
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;

    // More aggressive backoff to respect Dhan API rate limits
    // Start at 30s, then 60s, 120s, 240s, 300s (max 5 minutes)
    const baseDelay = this.config.reconnectInterval; // 30 seconds
    const delay = Math.min(baseDelay * Math.pow(2, this.reconnectAttempts - 1), 300000); // Max 5 minutes

    // Scheduling reconnect attempt with exponential backoff

    this.reconnectTimer = setTimeout(() => {
      // Attempting WebSocket reconnection
      this.connect().then(connected => {
        if (connected && this.subscribedInstruments.size > 0) {
          // Re-subscribe to instruments
          this.subscribe(Array.from(this.subscribedInstruments));
        }
      });
    }, delay);
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
}

// Default Dhan WebSocket configuration
export const DEFAULT_DHAN_WEBSOCKET_CONFIG: Partial<DhanWebSocketConfig> = {
  reconnectInterval: 30000, // 30 seconds (increased to respect rate limits)
  maxReconnectAttempts: 5, // Reduced attempts to avoid rate limiting
  heartbeatInterval: 60000, // 60 seconds (reduced frequency)
  version: 2, // Dhan API v2
  authType: 2 // Default auth type
};

// Legacy export for backward compatibility
export const DEFAULT_WEBSOCKET_CONFIG = DEFAULT_DHAN_WEBSOCKET_CONFIG;

// Export the new service as the main export
export const WebSocketMarketDataService = DhanWebSocketMarketDataService;
