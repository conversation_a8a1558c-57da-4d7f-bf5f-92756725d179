/**
 * Trading Signals API Route
 * Handles fetching trading signals and trade history
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

interface PerformanceRecord {
  id: string;
  user_id: string;
  date: string;
  total_signals: number;
  total_trades: number;
  successful_trades: number;
  failed_trades: number;
  win_rate: number;
  total_pnl: number;
  created_at: string;
  updated_at: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const type = searchParams.get('type') || 'signals'; // 'signals' or 'trades'
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('Trading signals GET request:', { userId, type, limit, offset });

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (type === 'signals') {
      // Get trading signals
      const { data: signals, error: signalsError, count } = await supabase
        .from('trading_signals')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (signalsError) {
        console.error('Error fetching signals:', signalsError);
        // Return empty array instead of error for better UX
        return NextResponse.json({
          success: true,
          data: {
            signals: [],
            total: 0,
            limit,
            offset
          }
        });
      }

      console.log(`Found ${signals?.length || 0} signals for user ${userId}`);
      return NextResponse.json({
        success: true,
        data: {
          signals: signals || [],
          total: count || 0,
          limit,
          offset
        }
      });

    } else if (type === 'trades') {
      // Get executed trades
      const { data: trades, error: tradesError, count } = await supabase
        .from('executed_trades')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (tradesError) {
        console.error('Error fetching trades:', tradesError);
        // Return empty array instead of error for better UX
        return NextResponse.json({
          success: true,
          data: {
            trades: [],
            total: 0,
            limit,
            offset
          }
        });
      }

      console.log(`Found ${trades?.length || 0} trades for user ${userId}`);
      return NextResponse.json({
        success: true,
        data: {
          trades: trades || [],
          total: count || 0,
          limit,
          offset
        }
      });

    } else if (type === 'performance') {
      // Get strategy performance
      const { data: performance, error: performanceError } = await supabase
        .from('strategy_performance')
        .select('*')
        .eq('user_id', userId)
        .order('date', { ascending: false })
        .limit(30); // Last 30 days

      if (performanceError) {
        console.error('Error fetching performance:', performanceError);
        // Return empty performance data instead of error
        const emptyPerformance: PerformanceRecord[] = [];
        const summary = calculatePerformanceSummary(emptyPerformance);

        return NextResponse.json({
          success: true,
          data: {
            performance: emptyPerformance,
            summary
          }
        });
      }

      // Calculate summary statistics
      const summary = calculatePerformanceSummary(performance || []);

      console.log(`Found ${performance?.length || 0} performance records for user ${userId}`);
      return NextResponse.json({
        success: true,
        data: {
          performance: performance || [],
          summary
        }
      });

    } else {
      return NextResponse.json(
        { error: 'Invalid type parameter' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error in trading signals API:', error);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Calculate performance summary statistics
 */
function calculatePerformanceSummary(performance: PerformanceRecord[]) {
  if (performance.length === 0) {
    return {
      totalSignals: 0,
      totalTrades: 0,
      successfulTrades: 0,
      failedTrades: 0,
      overallWinRate: 0,
      totalPnl: 0,
      avgDailySignals: 0,
      avgDailyTrades: 0,
      bestDay: null,
      worstDay: null
    };
  }

  const totals = performance.reduce((acc, day) => {
    acc.totalSignals += day.total_signals || 0;
    acc.totalTrades += day.total_trades || 0;
    acc.successfulTrades += day.successful_trades || 0;
    acc.failedTrades += day.failed_trades || 0;
    acc.totalPnl += parseFloat(String(day.total_pnl || 0));
    return acc;
  }, {
    totalSignals: 0,
    totalTrades: 0,
    successfulTrades: 0,
    failedTrades: 0,
    totalPnl: 0
  });

  const overallWinRate = totals.totalTrades > 0 
    ? Math.round((totals.successfulTrades / totals.totalTrades) * 100 * 100) / 100
    : 0;

  const avgDailySignals = Math.round((totals.totalSignals / performance.length) * 100) / 100;
  const avgDailyTrades = Math.round((totals.totalTrades / performance.length) * 100) / 100;

  // Find best and worst days by PnL
  const sortedByPnl = [...performance].sort((a, b) =>
    parseFloat(String(b.total_pnl || 0)) - parseFloat(String(a.total_pnl || 0))
  );

  return {
    totalSignals: totals.totalSignals,
    totalTrades: totals.totalTrades,
    successfulTrades: totals.successfulTrades,
    failedTrades: totals.failedTrades,
    overallWinRate,
    totalPnl: Math.round(totals.totalPnl * 100) / 100,
    avgDailySignals,
    avgDailyTrades,
    bestDay: sortedByPnl.length > 0 ? {
      date: sortedByPnl[0].date,
      pnl: parseFloat(String(sortedByPnl[0].total_pnl || 0)),
      trades: sortedByPnl[0].total_trades || 0
    } : null,
    worstDay: sortedByPnl.length > 0 ? {
      date: sortedByPnl[sortedByPnl.length - 1].date,
      pnl: parseFloat(String(sortedByPnl[sortedByPnl.length - 1].total_pnl || 0)),
      trades: sortedByPnl[sortedByPnl.length - 1].total_trades || 0
    } : null
  };
}

// Handle CORS if needed
export async function OPTIONS(_request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
