/**
 * Trading Configuration API Route
 * Handles getting and updating trading configuration settings
 */

import { NextRequest, NextResponse } from 'next/server';
import { tradingConfigService } from '@/lib/trading-config';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    console.log('Trading config GET request for userId:', userId);

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Try to get config with detailed logging
    console.log('Attempting to get trading config for user:', userId);
    const config = await tradingConfigService.getTradingConfig(userId);
    console.log('Config result:', config ? 'Found' : 'Not found');

    const stats = await tradingConfigService.getTradingStats(userId);
    console.log('Stats result:', stats ? 'Found' : 'Not found');

    if (!config) {
      console.log('No config found, returning 404');
      return NextResponse.json(
        { error: 'Trading configuration not found' },
        { status: 404 }
      );
    }

    console.log('Returning successful response');
    return NextResponse.json({
      success: true,
      data: {
        config,
        stats
      }
    });

  } catch (error) {
    console.error('Error in trading config GET API:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, updates } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const success = await tradingConfigService.updateTradingConfig(userId, updates);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update trading configuration' },
        { status: 500 }
      );
    }

    // Get updated config
    const config = await tradingConfigService.getTradingConfig(userId);
    const stats = await tradingConfigService.getTradingStats(userId);

    return NextResponse.json({
      success: true,
      data: {
        config,
        stats
      }
    });

  } catch (error) {
    console.error('Error in trading config PUT API:', error);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, action, value } = body;

    if (!userId || !action) {
      return NextResponse.json(
        { error: 'User ID and action are required' },
        { status: 400 }
      );
    }

    let success = false;

    switch (action) {
      case 'toggleAutoTrade':
        success = await tradingConfigService.toggleAutoTrade(userId);
        break;
      case 'switchTradingMode':
        if (!value || !['SANDBOX', 'LIVE'].includes(value)) {
          return NextResponse.json(
            { error: 'Valid trading mode is required' },
            { status: 400 }
          );
        }
        success = await tradingConfigService.switchTradingMode(userId, value);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    if (!success) {
      return NextResponse.json(
        { error: `Failed to ${action}` },
        { status: 500 }
      );
    }

    // Get updated config
    const config = await tradingConfigService.getTradingConfig(userId);
    const stats = await tradingConfigService.getTradingStats(userId);

    return NextResponse.json({
      success: true,
      data: {
        config,
        stats
      }
    });

  } catch (error) {
    console.error('Error in trading config POST API:', error);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle CORS if needed
export async function OPTIONS(_request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
