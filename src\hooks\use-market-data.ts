/**
 * Enhanced Custom hook for fetching and managing live market data
 * Supports WebSocket real-time data, historical data fallback, and market hours detection
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { UseMarketDataState, ProcessedMarketData } from '@/types/dhan';
import { getCombinedMarketStatus, MarketStatus } from '@/lib/market-hours';
import {
  getLastAvailableData,
  saveMarketDataCache,
  getCacheMetadata
} from '@/lib/historical-data-storage';
// import {
//   DhanWebSocketMarketDataService
// } from '@/lib/websocket-market-data';
import { webSocketManager } from '@/lib/websocket-manager';

interface MarketDataApiResponse {
  success: boolean;
  data?: ProcessedMarketData[];
  error?: string;
  details?: unknown;
  timestamp?: string;
}

export type DataSource = 'LIVE' | 'HISTORICAL' | 'DEMO' | 'WEBSOCKET';

export interface MarketDataStatus {
  dataSource: DataSource;
  isLive: boolean;
  lastUpdate: Date | null;
  marketStatus: {
    nse: MarketStatus;
    mcx: MarketStatus;
    anyOpen: boolean;
    allClosed: boolean;
  };
  websocketStatus?: {
    isConnected: boolean;
    isConnecting: boolean;
    reconnectAttempts: number;
  };
  cacheInfo?: {
    hasCache: boolean;
    isFromToday: boolean;
    ageInHours: number;
  };
}

interface UseMarketDataOptions {
  refreshInterval?: number; // in milliseconds, default 120 seconds (increased to avoid rate limits)
  autoRefresh?: boolean; // default true
  enableWebSocket?: boolean; // default true when markets are open
  fallbackToHistorical?: boolean; // default true
}

export function useMarketData(options: UseMarketDataOptions = {}): UseMarketDataState & { status: MarketDataStatus } {
  const {
    refreshInterval = 120000, // Increased to 2 minutes to reduce API calls
    autoRefresh = true,
    enableWebSocket = true,
    fallbackToHistorical = true
  } = options;

  const [data, setData] = useState<ProcessedMarketData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [dataSource, setDataSource] = useState<DataSource>('DEMO');
  const [marketStatus, setMarketStatus] = useState(getCombinedMarketStatus());

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const retryCountRef = useRef(0);
  const lastErrorRef = useRef<string | null>(null);
  const marketStatusIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsInitializingRef = useRef<boolean>(false);
  const wsSubscriptionIdRef = useRef<string | null>(null);
  const wsStatusCleanupRef = useRef<(() => void) | null>(null);

  // Load historical data on mount
  const loadHistoricalData = useCallback(() => {
    const historicalData = getLastAvailableData();
    if (historicalData && historicalData.length > 0) {
      console.log('Loading historical market data:', historicalData.length, 'instruments');
      // Ensure lastUpdated is a proper Date object for historical data
      const processedHistoricalData = historicalData.map(item => ({
        ...item,
        lastUpdated: new Date(item.lastUpdated)
      }));
      setData(processedHistoricalData);
      setDataSource('HISTORICAL');
      setLastUpdated(new Date(historicalData[0].capturedAt));
      setLoading(false);
      return true;
    }
    return false;
  }, []);

  const fetchMarketData = useCallback(async (forceRefresh = false) => {
    // PRIORITY 1: Check if WebSocket is providing live data
    const wsStatus = webSocketManager.getStatus();
    if (wsStatus.isConnected && wsStatus.subscribedInstruments.length > 0) {
      console.log('WebSocket is active, skipping REST API call');
      return; // WebSocket is handling live data
    }

    // PRIORITY 2: Use cached data if available and not forcing refresh
    if (!forceRefresh) {
      const cachedData = getLastAvailableData();
      if (cachedData && cachedData.length > 0) {
        const cacheMetadata = getCacheMetadata();
        const cacheAge = Date.now() - (cacheMetadata?.lastUpdate ? new Date(cacheMetadata.lastUpdate).getTime() : 0);
        const maxCacheAge = 5 * 60 * 1000; // 5 minutes

        if (cacheAge < maxCacheAge) {
          console.log('Using cached market data to avoid API calls');
          // Ensure lastUpdated is a proper Date object for cached data
          const processedCachedData = cachedData.map(item => ({
            ...item,
            lastUpdated: new Date(item.lastUpdated)
          }));
          setData(processedCachedData);
          setDataSource('HISTORICAL');
          setLastUpdated(new Date(getCacheMetadata()?.lastUpdate || new Date().toISOString()));
          setLoading(false);
          return;
        }
      }
    }

    // PRIORITY 3: Make REST API call only if necessary
    try {
      setError(null);
      if (forceRefresh) setLoading(true);
      console.log('Making REST API call for market data');

      const response = await fetch('/api/dhan/market-data', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result: MarketDataApiResponse = await response.json();

      if (!response.ok) {
        if (response.status === 429) {
          // Enhanced 429 handling
          console.warn('Rate limit exceeded (429), implementing extended backoff');
          const rateLimitError = 'Rate limit exceeded. Market data will resume automatically. Please wait before making more requests.';

          // Stop all API calls immediately
          if (intervalRef.current) {
            clearTimeout(intervalRef.current);
            intervalRef.current = null;
          }

          // Set extended retry delay (minimum 60 seconds)
          retryCountRef.current = Math.max(retryCountRef.current, 2);
          lastErrorRef.current = rateLimitError;

          throw new Error(rateLimitError);
        }
        throw new Error(result.error || `HTTP error! status: ${response.status}`);
      }

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to fetch market data');
      }

      if (mountedRef.current && result.data.length > 0) {
        // Ensure lastUpdated is a proper Date object (API serialization converts Date to string)
        const processedData = result.data.map(item => ({
          ...item,
          lastUpdated: new Date(item.lastUpdated)
        }));

        setData(processedData);
        setDataSource('LIVE');
        setLastUpdated(new Date());
        retryCountRef.current = 0;
        lastErrorRef.current = null;

        // Cache the live data for future use
        saveMarketDataCache(processedData, 'API');
        console.log('Market data fetched and cached successfully');
      } else if (mountedRef.current && result.data.length === 0) {
        // API returned empty data, try to load historical data
        if (fallbackToHistorical && !loadHistoricalData()) {
          // No historical data available, show demo data
          setDataSource('DEMO');
        }
      }
    } catch (err) {
      if (!mountedRef.current) return;

      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      lastErrorRef.current = errorMessage;
      retryCountRef.current++;

      console.error('Error fetching market data:', err);

      // Enhanced fallback strategy
      // 1. Try historical data first
      if (fallbackToHistorical && loadHistoricalData()) {
        console.log('Using historical data as fallback');
        return;
      }

      // 2. Try any cached data (even if old)
      const anyCachedData = getLastAvailableData();
      if (anyCachedData && anyCachedData.length > 0) {
        console.log('Using old cached data as fallback');
        // Ensure lastUpdated is a proper Date object for fallback cached data
        const processedFallbackData = anyCachedData.map(item => ({
          ...item,
          lastUpdated: new Date(item.lastUpdated)
        }));
        setData(processedFallbackData);
        setDataSource('HISTORICAL');
        setLastUpdated(new Date(getCacheMetadata()?.lastUpdate || new Date().toISOString()));
        return;
      }

      // 3. Finally, show demo data
      setDataSource('DEMO');
    } finally {
      setLoading(false);
    }
  }, [fallbackToHistorical, loadHistoricalData]);

  // WebSocket management using singleton manager
  const initializeWebSocket = useCallback(async () => {
    if (!enableWebSocket || wsSubscriptionIdRef.current || wsInitializingRef.current) return;

    wsInitializingRef.current = true;

    try {
      // Fetch WebSocket configuration from API
      const response = await fetch('/api/dhan/websocket-config');
      const result = await response.json();

      if (!response.ok || !result.available) {
        console.warn('Dhan WebSocket not available:', result.error);
        return;
      }

      // Initialize WebSocket manager (singleton ensures only one connection)
      const connected = await webSocketManager.initialize(result.config);

      if (connected) {
        console.log('WebSocket manager initialized successfully');

        // Subscribe to instruments using the manager
        const subscriptionId = webSocketManager.subscribe(
          ['NIFTY50', 'SILVER'],
          (marketData: ProcessedMarketData) => {
            if (mountedRef.current) {
              setData(prevData => {
                const updatedData = [...prevData];
                const index = updatedData.findIndex(item => item.symbol === marketData.symbol);
                if (index >= 0) {
                  updatedData[index] = marketData;
                } else {
                  updatedData.push(marketData);
                }
                return updatedData;
              });
              setDataSource('WEBSOCKET');
              setLastUpdated(new Date());
            }
          },
          `market-data-${Date.now()}`
        );

        wsSubscriptionIdRef.current = subscriptionId;

        // Set up status change handler
        const statusCleanup = webSocketManager.onStatusChange((status) => {
          if (status === 'error' || status === 'disconnected') {
            console.log('WebSocket status changed to:', status);
            // Fallback to REST API polling
            if (autoRefresh) {
              scheduleNextRefresh();
            }
          }
        });

        wsStatusCleanupRef.current = statusCleanup;

      } else {
        console.error('Failed to initialize WebSocket manager');
      }
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
    } finally {
      wsInitializingRef.current = false;
    }
  }, [enableWebSocket, autoRefresh]);

  const refetch = useCallback(async () => {
    setLoading(true);
    setError(null);
    await fetchMarketData(true);
  }, [fetchMarketData]);

  // Market status monitoring
  const updateMarketStatus = useCallback(() => {
    const newStatus = getCombinedMarketStatus();
    setMarketStatus(newStatus);

    // Smart data source switching based on market status
    if (newStatus.anyOpen && enableWebSocket && !wsSubscriptionIdRef.current) {
      // Markets are open, initialize WebSocket
      initializeWebSocket();
    } else if (!newStatus.anyOpen && wsSubscriptionIdRef.current) {
      // Markets are closed, unsubscribe and load historical data
      webSocketManager.unsubscribe(wsSubscriptionIdRef.current);
      wsSubscriptionIdRef.current = null;

      if (wsStatusCleanupRef.current) {
        wsStatusCleanupRef.current();
        wsStatusCleanupRef.current = null;
      }

      if (fallbackToHistorical) {
        loadHistoricalData();
      }
    }
  }, [enableWebSocket, initializeWebSocket, fallbackToHistorical, loadHistoricalData]);

  // Initial setup
  useEffect(() => {
    // Load historical data first (immediate display)
    if (fallbackToHistorical) {
      const hasHistoricalData = loadHistoricalData();
      if (!hasHistoricalData) {
        setDataSource('DEMO');
        setLoading(false);
      }
    } else {
      setDataSource('DEMO');
      setLoading(false);
    }

    // Update market status
    updateMarketStatus();

    // Try to fetch live data
    fetchMarketData();
  }, [fetchMarketData, loadHistoricalData, updateMarketStatus, fallbackToHistorical]);

  // Schedule next refresh (used by both auto-refresh and error recovery)
  const scheduleNextRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearTimeout(intervalRef.current);
    }

    let delay = refreshInterval;

    // Apply exponential backoff for rate limit errors
    if (lastErrorRef.current?.toLowerCase().includes('rate limit')) {
      // More aggressive backoff for rate limiting: start at 5 minutes, double each time
      delay = Math.min(300000 * Math.pow(2, retryCountRef.current), 1800000); // Max 30 minutes
      console.log(`Rate limit detected. Next retry in ${Math.round(delay/60000)} minutes`);
    }

    intervalRef.current = setTimeout(() => {
      // Only use REST API polling if WebSocket is not connected
      const wsStatus = webSocketManager.getStatus();
      if (!wsStatus.isConnected) {
        fetchMarketData();
      }
      scheduleNextRefresh();
    }, delay);
  }, [fetchMarketData, refreshInterval]);

  // Set up auto-refresh and market status monitoring
  useEffect(() => {
    if (!autoRefresh) return;

    // Start auto-refresh
    scheduleNextRefresh();

    // Monitor market status every minute
    marketStatusIntervalRef.current = setInterval(updateMarketStatus, 60000);

    return () => {
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
      if (marketStatusIntervalRef.current) {
        clearInterval(marketStatusIntervalRef.current);
      }
    };
  }, [autoRefresh, updateMarketStatus, fetchMarketData, refreshInterval, scheduleNextRefresh]);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;

      // Cleanup timers
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
      if (marketStatusIntervalRef.current) {
        clearInterval(marketStatusIntervalRef.current);
      }

      // Cleanup WebSocket subscription
      if (wsSubscriptionIdRef.current) {
        webSocketManager.unsubscribe(wsSubscriptionIdRef.current);
        wsSubscriptionIdRef.current = null;
      }

      if (wsStatusCleanupRef.current) {
        wsStatusCleanupRef.current();
        wsStatusCleanupRef.current = null;
      }
    };
  }, []);

  // Create status object
  const wsStatus = webSocketManager.getStatus();
  const status: MarketDataStatus = {
    dataSource,
    isLive: dataSource === 'WEBSOCKET' || (dataSource === 'LIVE' && marketStatus.anyOpen),
    lastUpdate: lastUpdated,
    marketStatus,
    websocketStatus: {
      isConnected: wsStatus.isConnected,
      isConnecting: wsStatus.isConnecting,
      reconnectAttempts: wsStatus.reconnectAttempts
    },
    cacheInfo: getCacheMetadata()
  };

  return {
    data,
    loading,
    error,
    lastUpdated,
    refetch,
    status,
  };
}

/**
 * Hook for fetching market data with custom refresh control and enhanced features
 */
export function useMarketDataWithControl(refreshInterval: number = 120000) {
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(true);
  const [enableWebSocket, setEnableWebSocket] = useState(true);

  const marketData = useMarketData({
    refreshInterval,
    autoRefresh: isAutoRefreshEnabled,
    enableWebSocket,
    fallbackToHistorical: true,
  });

  const toggleAutoRefresh = useCallback(() => {
    setIsAutoRefreshEnabled(prev => !prev);
  }, []);

  const toggleWebSocket = useCallback(() => {
    setEnableWebSocket(prev => !prev);
  }, []);

  const setRefreshInterval = useCallback((_interval: number) => {
    // This would require a more complex implementation to dynamically change interval
    // For now, we'll just return the current state
    return marketData;
  }, [marketData]);

  return {
    ...marketData,
    isAutoRefreshEnabled,
    enableWebSocket,
    toggleAutoRefresh,
    toggleWebSocket,
    setRefreshInterval,
  };
}
